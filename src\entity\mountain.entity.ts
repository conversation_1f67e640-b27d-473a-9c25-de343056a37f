import {
  Table,
  Column,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { RegionDict } from './region-dict.entity';
import { Photo } from './photo.entity';

export interface MountainAttributes {
  /** ID */
  id: number;
}

/**
 * 山塬表模型
 */
@Table({
  tableName: 'mountain',
  comment: '山塬表',
})
export class Mountain
  extends Model<MountainAttributes>
  implements MountainAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '编号',
  })
  code: string;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: false,
    comment: '山塬经度',
  })
  longitude: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: false,
    comment: '山塬纬度',
  })
  latitude: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '山塬高度',
  })
  height: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '相关历史文献记载',
    field: 'historical_records',
  })
  historicalRecords: string;

  @ForeignKey(() => RegionDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属区域ID',
    field: 'region_dict_id',
  })
  regionDictId: number;

  // 关联关系
  @BelongsTo(() => RegionDict, 'regionDictId')
  regionDict: RegionDict;

  @HasMany(() => Photo, 'mountainId')
  photos: Photo[];
}
