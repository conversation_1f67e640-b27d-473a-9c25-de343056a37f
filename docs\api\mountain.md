# 山塬管理接口

## 创建山塬

创建新的山塬数据。

### 接口信息

- **URL**: `/admin/mountain`
- **方法**: `POST`
- **认证**: 需要管理员权限

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 山塬名称，最大255字符 |
| code | string | 是 | 山塬编号，最大50字符 |
| longitude | number | 是 | 经度，范围-180到180 |
| latitude | number | 是 | 纬度，范围-90到90 |
| height | number | 是 | 高度（米），大于等于0 |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 是 | 所属区域ID |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/mountain" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
    "regionDictId": 1
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
    "regionDictId": 1,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "创建山塬成功"
}
```

---

## 更新山塬

更新指定山塬的信息。

### 接口信息

- **URL**: `/api/admin/mountain/{id}`
- **方法**: `PUT`
- **认证**: 需要管理员权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 山塬名称，最大255字符 |
| code | string | 否 | 山塬编号，最大50字符 |
| longitude | number | 否 | 经度，范围-180到180 |
| latitude | number | 否 | 纬度，范围-90到90 |
| height | number | 否 | 高度（米），大于等于0 |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 否 | 所属区域ID |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/api/admin/mountain/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "height": 2155,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一，位于陕西省华阴市。"
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2155,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一，位于陕西省华阴市。",
    "regionDictId": 1,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "更新山塬成功"
}
```

---

## 获取山塬列表

分页获取山塬列表。

### 接口信息

- **URL**: `/api/admin/data/mountain`
- **方法**: `GET`
- **认证**: 需要管理员权限

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/api/admin/data/mountain?page=1&pageSize=10&keyword=华山" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2155,
        "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一，位于陕西省华阴市。",
        "regionDictId": 1,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "regionDict": {
          "id": 1,
          "type": "region",
          "code": "guanzhong",
          "name": "关中地区"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "获取数据列表成功"
}
```

---

## 删除山塬

删除指定的山塬数据。

### 接口信息

- **URL**: `/api/admin/data/mountain/{id}`
- **方法**: `DELETE`
- **认证**: 需要管理员权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

### 请求示例

```bash
curl -X DELETE "http://localhost:7001/api/admin/data/mountain/1" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "success": true,
  "data": null,
  "message": "删除数据成功"
}
```

---

## 公共接口 - 获取山塬列表

前端展示用的山塬列表接口，无需认证。

### 接口信息

- **URL**: `/api/list/mountain`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/api/list/mountain?page=1&pageSize=10"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2155,
        "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一，位于陕西省华阴市。",
        "regionDictId": 1,
        "regionDict": {
          "id": 1,
          "type": "region",
          "code": "guanzhong",
          "name": "关中地区"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "获取山塬列表成功"
}
```

---

## 错误响应

### 数据不存在

```json
{
  "success": false,
  "message": "数据不存在",
  "code": 400
}
```

### 参数验证失败

```json
{
  "success": false,
  "message": "经度范围应在-180到180之间",
  "code": 400
}
```

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 山塬唯一标识 |
| name | string | 山塬名称 |
| code | string | 山塬编号 |
| longitude | number | 经度坐标 |
| latitude | number | 纬度坐标 |
| height | number | 海拔高度（米） |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域详细信息 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

## 使用说明

1. **坐标系统**: 使用WGS84坐标系统
2. **高度单位**: 统一使用米作为单位
3. **编号规则**: 建议使用有意义的编号，如"HS001"表示华山001
4. **区域关联**: 必须关联到已存在的区域字典项
5. **删除影响**: 删除山塬时会同时删除相关的照片和关系数据
