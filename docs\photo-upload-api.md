# 照片上传API文档

## 概述

照片上传功能已经升级为使用Midway.js的busboy组件，实现了文件上传和照片记录创建的合并处理。

## 新的上传接口

### 1. 单文件上传

**接口地址**: `POST /admin/photo/upload`

**请求方式**: `multipart/form-data`

**请求参数**:
- `file`: 上传的图片文件（必填）
- `photoName`: 照片名称（可选，默认使用文件名）
- `entityType`: 关联实体类型（可选）
  - `mountain`: 山塬
  - `waterSystem`: 水系
  - `historicalElement`: 历史要素
- `entityId`: 关联实体ID（可选，需要与entityType一起使用）

**响应示例**:
```json
{
  "url": "/public/uploads/2025/01/27/uuid-filename.jpg",
  "photoId": 123,
  "filename": "original-filename.jpg",
  "size": 1024000
}
```

### 2. 批量文件上传

**接口地址**: `POST /admin/photo/upload/batch`

**请求方式**: `multipart/form-data`

**请求参数**:
- `files`: 多个上传的图片文件（必填）
- `entityType`: 关联实体类型（可选）
- `entityId`: 关联实体ID（可选）

**响应示例**:
```json
{
  "message": "批量上传成功",
  "count": 3,
  "data": [
    {
      "url": "/public/uploads/2025/01/27/uuid-filename1.jpg",
      "photoId": 123,
      "filename": "file1.jpg",
      "size": 1024000
    },
    {
      "url": "/public/uploads/2025/01/27/uuid-filename2.jpg",
      "photoId": 124,
      "filename": "file2.jpg",
      "size": 2048000
    }
  ]
}
```

## 功能特性

### 1. 文件验证
- **文件类型**: 仅支持图片文件（.jpg, .jpeg, .png, .gif, .bmp, .webp）
- **文件大小**: 最大50MB
- **文件名**: 自动生成UUID文件名，避免冲突

### 2. 目录结构
- 文件按日期自动分类存储：`/uploads/YYYY/MM/DD/`
- 开发环境存储在项目的`public/uploads/`目录
- 生产环境可配置独立的存储路径

### 3. 数据库记录
- 自动创建照片记录到数据库
- 支持关联到山塬、水系、历史要素
- 返回照片ID用于后续操作

### 4. 临时文件管理
- 使用busboy的file模式，文件先存储为临时文件
- 自动清理5分钟后的临时文件
- 支持手动清理临时文件

## 前端使用示例

### JavaScript Fetch API
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('photoName', '自定义照片名称');
formData.append('entityType', 'mountain');
formData.append('entityId', '123');

fetch('/admin/photo/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('上传成功:', data);
});
```

### HTML表单
```html
<form action="/admin/photo/upload" method="post" enctype="multipart/form-data">
  <input type="file" name="file" accept="image/*" required>
  <input type="text" name="photoName" placeholder="照片名称">
  <select name="entityType">
    <option value="">选择关联类型</option>
    <option value="mountain">山塬</option>
    <option value="waterSystem">水系</option>
    <option value="historicalElement">历史要素</option>
  </select>
  <input type="number" name="entityId" placeholder="关联ID">
  <button type="submit">上传</button>
</form>
```

## 配置说明

### busboy配置
```typescript
// src/config/config.default.ts
busboy: {
  mode: 'file',
  whitelist: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  tmpdir: join(process.cwd(), 'temp'),
  cleanTimeout: 5 * 60 * 1000, // 5分钟后清理临时文件
}
```

### 上传路径配置
```typescript
upload: {
  uploadPath: process.env.UPLOAD_PATH || join(process.cwd(), 'uploads'),
  useProjectPath: process.env.NODE_ENV === 'development',
}
```

## 错误处理

常见错误及解决方案：

1. **文件类型不支持**: 检查文件扩展名是否在白名单中
2. **文件大小超限**: 检查文件是否超过50MB限制
3. **权限不足**: 确保请求包含有效的JWT token
4. **目录创建失败**: 检查服务器写入权限

## 迁移说明

从旧的upload组件迁移到busboy组件的主要变化：

1. **配置变更**: `upload` 配置改为 `busboy` 配置
2. **中间件**: 需要手动配置 `UploadMiddleware` 到路由
3. **类型定义**: `UploadFileInfo` 不再是泛型类型
4. **文件处理**: file模式下 `data` 字段是临时文件路径而不是Buffer

## 安全注意事项

1. **文件类型验证**: 严格限制上传文件类型
2. **文件大小限制**: 防止大文件攻击
3. **权限控制**: 所有上传接口都需要JWT认证
4. **文件名安全**: 使用UUID生成文件名，避免路径遍历攻击
