import { Controller, Post, Put, Del, Get, Body, Param, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { DictionaryService } from '../../service/dictionary.service';
import {
  CreateRegionDictDTO,
  UpdateRegionDictDTO,
  CreateTypeDictDTO,
  UpdateTypeDictDTO,
  CreateRelationshipDictDTO,
  UpdateRelationshipDictDTO,
} from '../../dto/entity.dto';

/**
 * 字典管理控制器
 */
@Controller('/admin/dictionary', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminDictionaryController {
  @Inject()
  dictionaryService: DictionaryService;

  // ==================== 区域字典管理 ====================

  /**
   * 创建区域字典
   */
  @Post('/region')
  @Validate()
  async createRegionDict(@Body() createDto: CreateRegionDictDTO) {
    const data = await this.dictionaryService.createRegionDict(createDto);
    return data;
  }

  /**
   * 更新区域字典
   */
  @Put('/region/:id')
  @Validate()
  async updateRegionDict(@Param('id') id: number, @Body() updateDto: UpdateRegionDictDTO) {
    const data = await this.dictionaryService.updateRegionDict(id, updateDto);
    return data;
  }

  /**
   * 删除区域字典
   */
  @Del('/region/:id')
  async deleteRegionDict(@Param('id') id: number) {
    await this.dictionaryService.deleteRegionDict(id);
    return { message: '删除成功' };
  }

  /**
   * 获取区域字典列表
   */
  @Get('/region')
  async getRegionDictList() {
    const data = await this.dictionaryService.getRegionDictList();
    return data;
  }

  /**
   * 获取区域字典树形结构
   */
  @Get('/region/tree')
  async getRegionDictTree() {
    const data = await this.dictionaryService.getRegionDictTree();
    return data;
  }

  // ==================== 类型字典管理 ====================

  /**
   * 创建类型字典
   */
  @Post('/type')
  @Validate()
  async createTypeDict(@Body() createDto: CreateTypeDictDTO) {
    const data = await this.dictionaryService.createTypeDict(createDto);
    return data;
  }

  /**
   * 更新类型字典
   */
  @Put('/type/:id')
  @Validate()
  async updateTypeDict(@Param('id') id: number, @Body() updateDto: UpdateTypeDictDTO) {
    const data = await this.dictionaryService.updateTypeDict(id, updateDto);
    return data;
  }

  /**
   * 删除类型字典
   */
  @Del('/type/:id')
  async deleteTypeDict(@Param('id') id: number) {
    await this.dictionaryService.deleteTypeDict(id);
    return { message: '删除成功' };
  }

  /**
   * 获取类型字典列表
   */
  @Get('/type')
  async getTypeDictList() {
    const data = await this.dictionaryService.getTypeDictList();
    return data;
  }

  /**
   * 获取类型字典树形结构
   */
  @Get('/type/tree')
  async getTypeDictTree() {
    const data = await this.dictionaryService.getTypeDictTree();
    return data;
  }

  // ==================== 关系字典管理 ====================

  /**
   * 创建关系字典
   */
  @Post('/relationship')
  @Validate()
  async createRelationshipDict(@Body() createDto: CreateRelationshipDictDTO) {
    const data = await this.dictionaryService.createRelationshipDict(createDto);
    return data;
  }

  /**
   * 更新关系字典
   */
  @Put('/relationship/:id')
  @Validate()
  async updateRelationshipDict(@Param('id') id: number, @Body() updateDto: UpdateRelationshipDictDTO) {
    const data = await this.dictionaryService.updateRelationshipDict(id, updateDto);
    return data;
  }

  /**
   * 删除关系字典
   */
  @Del('/relationship/:id')
  async deleteRelationshipDict(@Param('id') id: number) {
    await this.dictionaryService.deleteRelationshipDict(id);
    return { message: '删除成功' };
  }

  /**
   * 获取关系字典列表
   */
  @Get('/relationship')
  async getRelationshipDictList() {
    const data = await this.dictionaryService.getRelationshipDictList();
    return data;
  }
}
