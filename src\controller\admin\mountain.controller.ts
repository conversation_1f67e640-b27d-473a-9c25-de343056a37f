import { Controller, Post, Put, Del, Get, Body, Param, Query, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { MountainService } from '../../service/mountain.service';
import { PageQueryDTO } from '../../dto/common.dto';
import { CreateMountainDTO, UpdateMountainDTO } from '../../dto/entity.dto';

/**
 * 山塬管理控制器
 */
@Controller('/admin/mountain', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminMountainController {
  @Inject()
  mountainService: MountainService;

  /**
   * 创建山塬
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateMountainDTO) {
    const data = await this.mountainService.createMountain(createDto);
    return data;
  }

  /**
   * 更新山塬
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() updateDto: UpdateMountainDTO) {
    const data = await this.mountainService.updateMountain(id, updateDto);
    return data;
  }

  /**
   * 删除山塬
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    await this.mountainService.deleteMountain(id);
    return { message: '删除成功' };
  }

  /**
   * 获取山塬列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO) {
    const data = await this.mountainService.findMountainList(query);
    return data;
  }

  /**
   * 获取山塬详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    const data = await this.mountainService.findById(id);
    if (!data) {
      throw new Error('山塬不存在');
    }
    return data;
  }

  /**
   * 批量导入山塬
   */
  @Post('/batch-import')
  @Validate()
  async batchImport(@Body() data: { mountains: CreateMountainDTO[] }) {
    await this.mountainService.batchImportMountains(data.mountains);
    return { message: '批量导入成功' };
  }

  /**
   * 获取山塬统计
   */
  @Get('/statistics/overview')
  async getStatistics(@Query('regionId') regionId?: number) {
    const data = await this.mountainService.getStatistics(regionId);
    return data;
  }

  /**
   * 根据区域获取山塬
   */
  @Get('/by-region/:regionId')
  async getByRegion(@Param('regionId') regionId: number) {
    const data = await this.mountainService.findByRegion(regionId);
    return data;
  }
}
