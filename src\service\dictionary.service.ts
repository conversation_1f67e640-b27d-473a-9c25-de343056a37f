import { Provide, Inject } from '@midwayjs/core';
import { RegionDict } from '../entity/region-dict.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';
import { CacheService } from './cache.service';
import {
  CreateRegionDictDTO,
  UpdateRegionDictDTO,
  CreateTypeDictDTO,
  UpdateTypeDictDTO,
  CreateRelationshipDictDTO,
  UpdateRelationshipDictDTO,
} from '../dto/entity.dto';

@Provide()
export class DictionaryService {
  @Inject()
  cacheService: CacheService;

  // ==================== 区域字典管理 ====================

  /**
   * 创建区域字典
   */
  async createRegionDict(createDto: CreateRegionDictDTO): Promise<RegionDict> {
    await this.validateRegionDictData(createDto);

    const regionDict = await RegionDict.create(createDto as any);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return regionDict;
  }

  /**
   * 更新区域字典
   */
  async updateRegionDict(
    id: number,
    updateDto: UpdateRegionDictDTO
  ): Promise<RegionDict> {
    await this.validateRegionDictData(updateDto);

    const regionDict = await RegionDict.findByPk(id);
    if (!regionDict) {
      throw new Error('区域字典不存在');
    }

    await regionDict.update(updateDto);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return regionDict;
  }

  /**
   * 删除区域字典
   */
  async deleteRegionDict(id: number): Promise<void> {
    const regionDict = await RegionDict.findByPk(id);
    if (!regionDict) {
      throw new Error('区域字典不存在');
    }

    // TODO: 检查是否有关联数据
    // 检查是否有子级区域
    const childCount = await RegionDict.count({ where: { parentId: id } });
    if (childCount > 0) {
      throw new Error('存在子级区域，无法删除');
    }

    await regionDict.destroy();

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 获取区域字典列表
   */
  async getRegionDictList(): Promise<RegionDict[]> {
    return await this.cacheService.getRegionDictCache();
  }

  /**
   * 获取区域字典树形结构
   */
  async getRegionDictTree(): Promise<any[]> {
    const regions = await this.getRegionDictList();
    return this.buildTree(regions, 'parentId');
  }

  // ==================== 类型字典管理 ====================

  /**
   * 创建类型字典
   */
  async createTypeDict(createDto: CreateTypeDictDTO): Promise<TypeDict> {
    await this.validateTypeDictData(createDto);

    const typeDict = await TypeDict.create(createDto as any);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return typeDict;
  }

  /**
   * 更新类型字典
   */
  async updateTypeDict(
    id: number,
    updateDto: UpdateTypeDictDTO
  ): Promise<TypeDict> {
    await this.validateTypeDictData(updateDto);

    const typeDict = await TypeDict.findByPk(id);
    if (!typeDict) {
      throw new Error('类型字典不存在');
    }

    await typeDict.update(updateDto);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return typeDict;
  }

  /**
   * 删除类型字典
   */
  async deleteTypeDict(id: number): Promise<void> {
    const typeDict = await TypeDict.findByPk(id);
    if (!typeDict) {
      throw new Error('类型字典不存在');
    }

    // 检查是否有子级类型
    const childCount = await TypeDict.count({ where: { parentId: id } });
    if (childCount > 0) {
      throw new Error('存在子级类型，无法删除');
    }

    // TODO: 检查是否有关联的历史要素

    await typeDict.destroy();

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 获取类型字典列表
   */
  async getTypeDictList(): Promise<TypeDict[]> {
    return await this.cacheService.getTypeDictCache();
  }

  /**
   * 获取类型字典树形结构
   */
  async getTypeDictTree(): Promise<any[]> {
    const types = await this.getTypeDictList();
    return this.buildTree(types, 'parentId');
  }

  // ==================== 关系字典管理 ====================

  /**
   * 创建关系字典
   */
  async createRelationshipDict(
    createDto: CreateRelationshipDictDTO
  ): Promise<RelationshipDict> {
    await this.validateRelationshipDictData(createDto);

    const relationshipDict = await RelationshipDict.create(createDto as any);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return relationshipDict;
  }

  /**
   * 更新关系字典
   */
  async updateRelationshipDict(
    id: number,
    updateDto: UpdateRelationshipDictDTO
  ): Promise<RelationshipDict> {
    await this.validateRelationshipDictData(updateDto);

    const relationshipDict = await RelationshipDict.findByPk(id);
    if (!relationshipDict) {
      throw new Error('关系字典不存在');
    }

    await relationshipDict.update(updateDto);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return relationshipDict;
  }

  /**
   * 删除关系字典
   */
  async deleteRelationshipDict(id: number): Promise<void> {
    const relationshipDict = await RelationshipDict.findByPk(id);
    if (!relationshipDict) {
      throw new Error('关系字典不存在');
    }

    // TODO: 检查是否有关联的关系数据

    await relationshipDict.destroy();

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 获取关系字典列表
   */
  async getRelationshipDictList(): Promise<RelationshipDict[]> {
    return await this.cacheService.getRelationshipDictCache();
  }

  // ==================== 通用方法 ====================

  /**
   * 构建树形结构
   */
  private buildTree(items: any[], parentKey: string): any[] {
    const tree: any[] = [];
    const map = new Map();

    // 创建映射
    items.forEach(item => {
      map.set(item.id, { ...item, children: [] });
    });

    // 构建树形结构
    items.forEach(item => {
      const node = map.get(item.id);
      if (item[parentKey] && map.has(item[parentKey])) {
        map.get(item[parentKey]).children.push(node);
      } else {
        tree.push(node);
      }
    });

    return tree;
  }

  /**
   * 验证区域字典数据
   */
  private async validateRegionDictData(
    data: CreateRegionDictDTO | UpdateRegionDictDTO
  ): Promise<void> {
    if (!data.regionCode || data.regionCode.trim().length === 0) {
      throw new Error('区域编码不能为空');
    }

    if (!data.regionName || data.regionName.trim().length === 0) {
      throw new Error('区域名称不能为空');
    }

    // TODO: 验证编码唯一性
  }

  /**
   * 验证类型字典数据
   */
  private async validateTypeDictData(
    data: CreateTypeDictDTO | UpdateTypeDictDTO
  ): Promise<void> {
    if (!data.typeCode || data.typeCode.trim().length === 0) {
      throw new Error('类型编码不能为空');
    }

    if (!data.typeName || data.typeName.trim().length === 0) {
      throw new Error('类型名称不能为空');
    }

    // TODO: 验证编码唯一性
  }

  /**
   * 验证关系字典数据
   */
  private async validateRelationshipDictData(
    data: CreateRelationshipDictDTO | UpdateRelationshipDictDTO
  ): Promise<void> {
    if (!data.relationCode || data.relationCode.trim().length === 0) {
      throw new Error('关系编码不能为空');
    }

    if (!data.relationName || data.relationName.trim().length === 0) {
      throw new Error('关系名称不能为空');
    }

    // TODO: 验证编码唯一性
  }
}
