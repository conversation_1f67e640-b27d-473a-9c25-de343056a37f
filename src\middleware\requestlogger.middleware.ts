/*
 * @Description: 请求日志中间件
 * @Date: 2025-01-09 08:43:18
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-22 11:30:04
 */
import { IMiddleware, Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';

@Middleware()
export class RequestLoggerMiddleware
  implements IMiddleware<Context, NextFunction>
{
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      console.dir({
        url: ctx.request.url,
        method: ctx.request.method,
        body: ctx.request.body || {},
        query: ctx.request.query || {},
        params: ctx.params,
        // headers: ctx.request.headers,
        ip: ctx.request.ip,
      });
      await next();
    };
  }

  ignore(): boolean {
    return false;
  }

  static getName(): string {
    return 'API_REQUEST_LOGGER';
  }
}
