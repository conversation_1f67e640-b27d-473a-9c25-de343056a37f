# 山塬管理 API 文档

## 概述

山塬管理模块提供山塬数据的完整管理功能，包括创建、更新、删除、查询等操作，支持按区域筛选和批量导入。

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 创建山塬

### 接口信息

- **URL**: `/admin/mountain`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 山塬名称，最大255字符 |
| code | string | 是 | 山塬编号，最大50字符 |
| longitude | number | 是 | 经度，范围-180到180 |
| latitude | number | 是 | 纬度，范围-90到90 |
| height | number | 是 | 高度（米），大于等于0 |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 是 | 所属区域ID |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/mountain" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
    "regionDictId": 1
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
    "regionDictId": 1,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

---

## 更新山塬

### 接口信息

- **URL**: `/admin/mountain/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 山塬名称 |
| code | string | 否 | 山塬编号 |
| longitude | number | 否 | 经度 |
| latitude | number | 否 | 纬度 |
| height | number | 否 | 高度（米） |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 否 | 所属区域ID |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/admin/mountain/1" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "height": 2155,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一，位于陕西省华阴市。"
  }'
```

---

## 删除山塬

### 接口信息

- **URL**: `/admin/mountain/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

### 请求示例

```bash
curl -X DELETE "http://localhost:7001/admin/mountain/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "删除成功"
  }
}
```

---

## 获取山塬列表

### 接口信息

- **URL**: `/admin/mountain`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/mountain?page=1&pageSize=10&keyword=华山" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2154,
        "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
        "regionDictId": 1,
        "regionDict": {
          "id": 1,
          "regionName": "关中地区",
          "regionCode": "GUANZHONG"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

## 获取山塬详情

### 接口信息

- **URL**: `/admin/mountain/{id}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/mountain/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
    "regionDictId": 1,
    "regionDict": {
      "id": 1,
      "regionName": "关中地区",
      "regionCode": "GUANZHONG"
    },
    "photos": [
      {
        "id": 1,
        "name": "华山主峰",
        "url": "/public/uploads/2024/01/15/huashan_1642234567890.jpg"
      }
    ]
  }
}
```

---

## 批量导入山塬

### 接口信息

- **URL**: `/admin/mountain/batch-import`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mountains | array | 是 | 山塬数据数组，每个元素包含创建山塬所需的所有字段 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/mountain/batch-import" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "mountains": [
      {
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2154,
        "regionDictId": 1
      },
      {
        "name": "终南山",
        "code": "ZNS001",
        "longitude": 108.9633,
        "latitude": 33.9617,
        "height": 2604,
        "regionDictId": 1
      }
    ]
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "批量导入成功"
  }
}
```

---

## 获取山塬统计

### 接口信息

- **URL**: `/admin/mountain/statistics/overview`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选特定区域的统计数据 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/mountain/statistics/overview?regionId=1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 25,
    "byRegion": [
      {
        "regionId": 1,
        "regionName": "关中地区",
        "count": 15
      },
      {
        "regionId": 2,
        "regionName": "陕北地区",
        "count": 10
      }
    ]
  }
}
```

---

## 根据区域获取山塬

### 接口信息

- **URL**: `/admin/mountain/by-region/{regionId}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 是 | 区域ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/mountain/by-region/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "华山",
      "code": "HS001",
      "longitude": 110.0910,
      "latitude": 34.4880,
      "height": 2154,
      "regionDictId": 1
    },
    {
      "id": 2,
      "name": "终南山",
      "code": "ZNS001",
      "longitude": 108.9633,
      "latitude": 33.9617,
      "height": 2604,
      "regionDictId": 1
    }
  ]
}
```

---

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 山塬唯一标识 |
| name | string | 山塬名称 |
| code | string | 山塬编号 |
| longitude | number | 经度坐标（WGS84） |
| latitude | number | 纬度坐标（WGS84） |
| height | number | 海拔高度（米） |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域详细信息 |
| photos | array | 关联的照片列表 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

---

## 注意事项

1. **坐标系统**: 使用WGS84坐标系统
2. **高度单位**: 统一使用米作为单位
3. **编号规则**: 建议使用有意义的编号，如"HS001"表示华山001
4. **区域关联**: 必须关联到已存在的区域字典项
5. **删除影响**: 删除山塬时会同时删除相关的照片和关系数据
6. **批量导入**: 支持批量导入，但需要确保数据格式正确
7. **权限控制**: 所有管理接口都需要管理员权限
