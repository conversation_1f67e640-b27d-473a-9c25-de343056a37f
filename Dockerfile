# 智慧营建系统 Dockerfile

# 使用官方Node.js运行时作为基础镜像
FROM node:16-alpine

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S midway -u 1001

# 创建必要的目录并设置权限
RUN mkdir -p /app/public/uploads && chown -R midway:nodejs /app

# 切换到非root用户
USER midway

# 暴露端口
EXPOSE 7001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:7001/api/dictionary || exit 1

# 启动应用
CMD ["npm", "start"]
