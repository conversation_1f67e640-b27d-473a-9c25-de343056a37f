import { Controller, Get, Inject } from '@midwayjs/core';
import { DictionaryService } from '../../service/dictionary.service';

/**
 * 字典数据公开接口
 */
@Controller('/public/dictionary')
export class PublicDictionaryController {
  @Inject()
  dictionaryService: DictionaryService;

  /**
   * 获取区域字典列表
   */
  @Get('/region')
  async getRegionDict() {
    const data = await this.dictionaryService.getRegionDictList();
    return data;
  }

  /**
   * 获取区域字典树形结构
   */
  @Get('/region/tree')
  async getRegionDictTree() {
    const data = await this.dictionaryService.getRegionDictTree();
    return data;
  }

  /**
   * 获取类型字典列表
   */
  @Get('/type')
  async getTypeDict() {
    const data = await this.dictionaryService.getTypeDictList();
    return data;
  }

  /**
   * 获取类型字典树形结构
   */
  @Get('/type/tree')
  async getTypeDictTree() {
    const data = await this.dictionaryService.getTypeDictTree();
    return data;
  }

  /**
   * 获取关系字典列表
   */
  @Get('/relationship')
  async getRelationshipDict() {
    const data = await this.dictionaryService.getRelationshipDictList();
    return data;
  }
}
