# 数据查询接口

## 概述

提供各类实体数据的查询接口，包括山塬、水系、历史要素的列表和详情查询。

---

## 获取山塬列表

### 接口信息

- **URL**: `/public/data/mountain`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/mountain?page=1&pageSize=10&keyword=华山"
```

### 响应示例

```json
{
  "data": [
    {
      "id": 1,
      "name": "华山",
      "code": "HS001",
      "longitude": 110.0910,
      "latitude": 34.4880,
      "height": 2154,
      "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
      "regionDictId": 1
    }
  ],
  "total": 1,
  "page": 1,
  "pageSize": 10
}
```

---

## 获取山塬详情

### 接口信息

- **URL**: `/public/data/mountain/{id}`
- **方法**: `GET`
- **认证**: 无需认证

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/mountain/1"
```

### 响应示例

```json
{
  "id": 1,
  "name": "华山",
  "code": "HS001",
  "longitude": 110.0910,
  "latitude": 34.4880,
  "height": 2154,
  "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
  "regionDictId": 1,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

---

## 获取水系列表

### 接口信息

- **URL**: `/public/data/water-system`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/water-system?page=1&pageSize=10"
```

---

## 获取历史要素列表

### 接口信息

- **URL**: `/public/data/historical-element`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |
| typeId | number | 否 | 类型ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/historical-element?page=1&pageSize=10&typeId=1"
```

---

## 根据区域获取数据

### 获取区域内的山塬

- **URL**: `/public/data/mountain/by-region/{regionId}`
- **方法**: `GET`

### 获取区域内的水系

- **URL**: `/public/data/water-system/by-region/{regionId}`
- **方法**: `GET`

### 根据类型获取历史要素

- **URL**: `/public/data/historical-element/by-type/{typeId}`
- **方法**: `GET`

### 请求示例

```bash
# 获取区域1的所有山塬
curl -X GET "http://localhost:7001/public/data/mountain/by-region/1"

# 获取类型1的所有历史要素
curl -X GET "http://localhost:7001/public/data/historical-element/by-type/1"
```

---

## 错误响应

### 数据不存在

```json
{
  "message": "数据不存在",
  "statusCode": 404,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/public/data/mountain/999"
}
```

### 参数验证失败

```json
{
  "message": "页码必须大于0",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/public/data/mountain"
}
```

## 使用场景

1. **列表展示**: 前端列表页面数据加载
2. **详情查看**: 点击列表项查看详细信息
3. **搜索筛选**: 按关键词、区域、类型筛选数据
4. **分页浏览**: 大量数据的分页展示
5. **关联查询**: 根据区域或类型获取相关数据
