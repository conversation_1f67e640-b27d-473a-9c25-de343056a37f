import { MidwayConfig } from '@midwayjs/core';
import { join } from 'path';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1755055792483_4120',
  koa: {
    port: 7001,
  },
  // Sequelize 数据库配置
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '***********',
        port: 3306,
        username: 'remote_user',
        password: 'Aa@123456',
        database: 'zhi_hui_ying_jian',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          // max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        logging: false,
        // 数据库连接选项
        dialectOptions: {
          connectTimeout: 60000,
          supportBigNumbers: true,
          bigNumberStrings: true,
          debug: false,
        },
      },
    },
  },
  // Redis 配置
  redis: {
    clients: {
      default: {
        port: 6379,
        host: '127.0.0.1',
        password: 'Aa@123456',
        db: 0,
      },
      dictionary: {
        port: 6379,
        host: '127.0.0.1',
        password: 'Aa@123456',
        db: 1,
      },
    },
  },
  // JWT 配置
  jwt: {
    secret: 'zhi-hui-ying-jian-secret-key',
    expiresIn: '7d',
  },
  // 文件上传配置
  busboy: {
    mode: 'file',
    whitelist: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
    limits: {
      fileSize: 50 * 1024 * 1024, // 50MB
    },
    tmpdir: join(process.cwd(), 'temp'),
    cleanTimeout: 5 * 60 * 1000, // 5分钟后清理临时文件
  },
  // 上传文件存储路径配置
  upload: {
    uploadPath: process.env.UPLOAD_PATH || join(process.cwd(), 'uploads'),
    // 是否使用项目内路径（开发环境）
    // useProjectPath: process.env.NODE_ENV === 'development',
    // 文件访问基础URL
    baseUrl: 'http://localhost:7001/uploads',
  },
} as MidwayConfig;
