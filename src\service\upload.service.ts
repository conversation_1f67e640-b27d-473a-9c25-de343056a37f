import { Provide, Inject, Config } from '@midwayjs/core';
import { UploadFileInfo } from '@midwayjs/busboy';
import { join } from 'path';
import { promises as fs } from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { PhotoService } from './photo.service';
import { CreatePhotoDTO } from '../dto/entity.dto';

@Provide()
export class UploadService {
  @Inject()
  photoService: PhotoService;

  @Config('upload')
  uploadConfig: any;
  /**
   * 上传单个文件
   */
  async uploadFile(
    file: UploadFileInfo,
    options?: {
      createPhotoRecord?: boolean;
      photoName?: string;
      entityType?: 'mountain' | 'waterSystem' | 'historicalElement';
      entityId?: number;
    }
  ): Promise<{ url: string; photoId?: number }> {
    // 生成唯一文件名
    const fileExtension = this.getFileExtension(file.filename);
    const fileName = `${uuidv4()}${fileExtension}`;

    // 按日期创建目录
    const today = new Date();
    const dateDir = `${today.getFullYear()}/${String(
      today.getMonth() + 1
    ).padStart(2, '0')}/${String(today.getDate()).padStart(2, '0')}`;

    // 使用配置的上传路径
    const baseUploadDir = this.uploadConfig.useProjectPath
      ? join(process.cwd(), 'public', 'uploads')
      : this.uploadConfig.uploadPath;

    // 确保基础上传目录存在
    await this.ensureDirectoryExists(baseUploadDir);

    const uploadDir = join(baseUploadDir, dateDir);

    // 确保完整目录路径存在
    await this.ensureDirectoryExists(uploadDir);

    // 文件保存路径
    const filePath = join(uploadDir, fileName);
    const relativePath = this.uploadConfig.useProjectPath
      ? `/public/uploads/${dateDir}/${fileName}`
      : `/uploads/${dateDir}/${fileName}`;

    // 在busboy的file模式下，file.data是临时文件路径，直接复制文件
    if (typeof file.data === 'string') {
      await fs.copyFile(file.data, filePath);
    } else {
      throw new Error('不支持的文件数据类型');
    }

    let photoId: number | undefined;

    // 如果需要创建photo记录
    if (options?.createPhotoRecord) {
      const photoDto: CreatePhotoDTO = {
        name: options.photoName || file.filename,
        url: relativePath,
      };

      // 设置关联实体
      if (options.entityType && options.entityId) {
        switch (options.entityType) {
          case 'mountain':
            photoDto.mountainId = options.entityId;
            break;
          case 'waterSystem':
            photoDto.waterSystemId = options.entityId;
            break;
          case 'historicalElement':
            photoDto.historicalElementId = options.entityId;
            break;
        }
      }

      const photo = await this.photoService.createPhoto(photoDto);
      photoId = photo.id;
    }

    return { url: relativePath, photoId };
  }

  /**
   * 上传多个文件
   */
  async uploadFiles(
    files: UploadFileInfo[],
    options?: {
      createPhotoRecords?: boolean;
      entityType?: 'mountain' | 'waterSystem' | 'historicalElement';
      entityId?: number;
    }
  ): Promise<{ url: string; photoId?: number; filename: string }[]> {
    const uploadPromises = files.map(file =>
      this.uploadFile(file, {
        createPhotoRecord: options?.createPhotoRecords,
        photoName: file.filename,
        entityType: options?.entityType,
        entityId: options?.entityId,
      }).then(result => ({
        ...result,
        filename: file.filename,
      }))
    );
    return await Promise.all(uploadPromises);
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
  }

  /**
   * 验证文件类型
   */
  validateFileType(
    filename: string,
    allowedTypes: string[] = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  ): boolean {
    const extension = this.getFileExtension(filename).toLowerCase();
    return allowedTypes.includes(extension);
  }

  /**
   * 验证文件大小
   */
  validateFileSize(
    fileSize: number,
    maxSize: number = 50 * 1024 * 1024
  ): boolean {
    // 默认50MB
    return fileSize <= maxSize;
  }

  /**
   * 确保目录存在，如果不存在则创建
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      // 检查目录是否存在
      await fs.access(dirPath);
    } catch (error) {
      // 目录不存在，创建目录
      try {
        await fs.mkdir(dirPath, { recursive: true });
        console.log(`✅ 创建上传目录: ${dirPath}`);
      } catch (createError) {
        console.error(`❌ 创建目录失败: ${dirPath}`, createError);
        throw new Error(`无法创建上传目录: ${dirPath}`);
      }
    }
  }
}
