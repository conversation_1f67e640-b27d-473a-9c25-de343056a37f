# 仪表盘 API 文档

## 概述

仪表盘模块提供数据可视化和统计分析功能，包括地图数据展示、统计分析、数据概览等接口，支持多维度的数据查询和展示。

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 地图数据接口

### 获取地图数据

#### 接口信息

- **URL**: `/public/map/data`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 数据类型，可选值：mountain、water_system、historical_element |
| regionId | number | 否 | 区域ID，筛选指定区域的数据 |
| typeId | number | 否 | 类型ID，筛选指定类型的历史要素 |

#### 请求示例

```bash
# 获取所有地图数据
curl -X GET "http://localhost:7001/public/map/data"

# 获取指定类型的数据
curl -X GET "http://localhost:7001/public/map/data?type=mountain"

# 获取指定区域的数据
curl -X GET "http://localhost:7001/public/map/data?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "mountains": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2154
      }
    ],
    "waterSystems": [
      {
        "id": 1,
        "name": "渭河",
        "code": "WH001",
        "longitude": 108.9633,
        "latitude": 34.2658,
        "lengthArea": "818公里"
      }
    ],
    "historicalElements": [
      {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001",
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "constructionTime": "652-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

### 获取实体详情

#### 接口信息

- **URL**: `/public/map/detail`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 数据类型，可选值：mountain、water_system、historical_element |
| id | number | 是 | 实体ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/map/detail?type=mountain&id=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "entity": {
      "id": 1,
      "name": "华山",
      "code": "HS001",
      "longitude": 110.0910,
      "latitude": 34.4880,
      "height": 2154,
      "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
      "regionDictId": 1
    },
    "photos": [],
    "relationships": []
  }
}
```

### 获取地图统计数据

#### 接口信息

- **URL**: `/public/map/statistics`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选特定区域的统计数据 |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/map/statistics?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "mountains": {
      "total": 25,
      "byRegion": [
        {
          "regionId": 1,
          "regionName": "关中地区",
          "count": 15
        }
      ]
    },
    "waterSystems": {
      "total": 15,
      "totalLength": 2500,
      "byRegion": [
        {
          "regionId": 1,
          "regionName": "关中地区",
          "count": 10
        }
      ]
    },
    "historicalElements": {
      "total": 50,
      "byType": [
        {
          "typeId": 1,
          "typeName": "佛塔",
          "count": 15
        }
      ],
      "byRegion": [
        {
          "regionId": 1,
          "regionName": "关中地区",
          "count": 35
        }
      ]
    },
    "total": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    }
  }
}
```

---

## 统计分析接口

### 获取基础统计数据

#### 接口信息

- **URL**: `/public/statistic/basic`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |
| startTime | string | 否 | 开始时间，ISO日期格式 |
| endTime | string | 否 | 结束时间，ISO日期格式 |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/basic?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "counts": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionStats": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      }
    ],
    "timelineData": [
      {
        "year": 652,
        "count": 1,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historical_element"
          }
        ]
      }
    ]
  }
}
```

### 获取综合统计报告

#### 接口信息

- **URL**: `/public/statistic/comprehensive`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/comprehensive?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "basic": {
      "counts": {
        "mountain": 25,
        "waterSystem": 15,
        "historicalElement": 50
      },
      "regionStats": [],
      "timelineData": []
    },
    "detailed": {
      "mountains": {
        "total": 25,
        "byRegion": []
      },
      "waterSystems": {
        "total": 15,
        "totalLength": 2500,
        "byRegion": []
      },
      "historicalElements": {
        "total": 50,
        "byType": [],
        "byRegion": []
      }
    },
    "summary": {
      "totalEntities": 90,
      "regionCoverage": 3,
      "timeSpan": {
        "earliest": 652,
        "latest": 1644,
        "span": 992
      }
    }
  }
}
```

### 获取时间轴数据

#### 接口信息

- **URL**: `/public/statistic/timeline`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/timeline?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "year": 652,
      "count": 1,
      "elements": [
        {
          "id": 1,
          "name": "大雁塔",
          "type": "historical_element"
        }
      ]
    },
    {
      "year": 707,
      "count": 1,
      "elements": [
        {
          "id": 2,
          "name": "小雁塔",
          "type": "historical_element"
        }
      ]
    }
  ]
}
```

### 获取区域分布统计

#### 接口信息

- **URL**: `/public/statistic/region-distribution`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/region-distribution"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "region": "关中地区",
      "regionId": 1,
      "mountainCount": 15,
      "waterSystemCount": 10,
      "historicalElementCount": 35,
      "total": 60
    },
    {
      "region": "陕北地区",
      "regionId": 2,
      "mountainCount": 10,
      "waterSystemCount": 5,
      "historicalElementCount": 15,
      "total": 30
    }
  ]
}
```

### 获取数据概览

#### 接口信息

- **URL**: `/public/statistic/overview`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/overview?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalCounts": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionStats": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      }
    ],
    "timelineData": [
      {
        "year": 652,
        "count": 1,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historical_element"
          }
        ]
      }
    ]
  }
}
```

---

## 数据字段说明

### 地图数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| mountains | array | 山塬数据列表 |
| waterSystems | array | 水系数据列表 |
| historicalElements | array | 历史要素数据列表 |

### 统计数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| counts | object | 各类型数据数量统计 |
| regionStats | array | 区域分布统计 |
| timelineData | array | 时间轴数据 |
| total | object | 总计数据 |

### 时间轴数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| year | number | 年份 |
| count | number | 该年份的要素数量 |
| elements | array | 该年份的要素列表 |

---

## 使用场景

1. **地图可视化**: 使用地图数据接口在地图上展示各类地理要素
2. **数据统计**: 使用统计接口展示数据概览和分析报告
3. **时间轴展示**: 使用时间轴数据展示历史发展脉络
4. **区域分析**: 使用区域分布统计分析不同区域的数据分布
5. **综合报告**: 使用综合统计报告生成完整的数据分析报告

---

## 注意事项

1. **无需认证**: 所有仪表盘接口都是公共接口，无需认证
2. **数据筛选**: 支持按区域、类型、时间范围等多维度筛选
3. **实时数据**: 数据来源于实时数据库，确保数据的准确性
4. **性能优化**: 大数据量查询时建议使用分页或筛选条件
5. **缓存机制**: 部分统计数据可能有缓存，更新可能有延迟
6. **坐标系统**: 地理坐标使用WGS84坐标系统
7. **时间格式**: 时间数据使用ISO 8601格式
