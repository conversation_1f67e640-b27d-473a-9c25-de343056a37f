# 字典管理 API 文档

## 概述

字典管理模块提供区域字典、类型字典、关系字典的管理功能，支持增删改查操作。字典数据用于系统中各种下拉选择和数据分类。

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 区域字典管理

### 创建区域字典

#### 接口信息

- **URL**: `/admin/dictionary/region`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionCode | string | 是 | 区域编码，最大50字符，唯一 |
| regionName | string | 是 | 区域名称，最大255字符 |
| parentId | number | 否 | 父级区域ID |
| status | number | 否 | 状态，0禁用 1启用，默认1 |
| sort | number | 否 | 排序号 |
| regionDesc | string | 否 | 区域描述 |

#### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/dictionary/region" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "regionCode": "GUANZHONG",
    "regionName": "关中地区",
    "status": 1,
    "sort": 1,
    "regionDesc": "陕西关中地区"
  }'
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "regionCode": "GUANZHONG",
    "regionName": "关中地区",
    "parentId": null,
    "status": 1,
    "sort": 1,
    "regionDesc": "陕西关中地区",
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

### 更新区域字典

#### 接口信息

- **URL**: `/admin/dictionary/region/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 区域字典ID |

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionCode | string | 否 | 区域编码 |
| regionName | string | 否 | 区域名称 |
| parentId | number | 否 | 父级区域ID |
| status | number | 否 | 状态 |
| sort | number | 否 | 排序号 |
| regionDesc | string | 否 | 区域描述 |

### 删除区域字典

#### 接口信息

- **URL**: `/admin/dictionary/region/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "删除成功"
  }
}
```

### 获取区域字典列表

#### 接口信息

- **URL**: `/admin/dictionary/region` 或 `/public/dictionary/region`
- **方法**: `GET`
- **认证**: 管理端需要认证，公共接口无需认证

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "regionCode": "GUANZHONG",
      "regionName": "关中地区",
      "parentId": null,
      "status": 1,
      "sort": 1,
      "regionDesc": "陕西关中地区"
    }
  ]
}
```

### 获取区域字典树形结构

#### 接口信息

- **URL**: `/admin/dictionary/region/tree` 或 `/public/dictionary/region/tree`
- **方法**: `GET`
- **认证**: 管理端需要认证，公共接口无需认证

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "regionCode": "GUANZHONG",
      "regionName": "关中地区",
      "parentId": null,
      "status": 1,
      "sort": 1,
      "children": [
        {
          "id": 2,
          "regionCode": "XIAN",
          "regionName": "西安市",
          "parentId": 1,
          "status": 1,
          "sort": 1,
          "children": []
        }
      ]
    }
  ]
}
```

---

## 类型字典管理

### 创建类型字典

#### 接口信息

- **URL**: `/admin/dictionary/type`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| typeCode | string | 是 | 类型编码，最大50字符，唯一 |
| typeName | string | 是 | 类型名称，最大255字符 |
| parentId | number | 否 | 父级类型ID |
| status | number | 否 | 状态，0禁用 1启用，默认1 |
| sort | number | 否 | 排序号 |
| typeDesc | string | 否 | 类型描述 |

#### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/dictionary/type" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "typeCode": "TEMPLE",
    "typeName": "寺庙",
    "status": 1,
    "sort": 1,
    "typeDesc": "历史寺庙建筑"
  }'
```

### 更新类型字典

#### 接口信息

- **URL**: `/admin/dictionary/type/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 删除类型字典

#### 接口信息

- **URL**: `/admin/dictionary/type/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

### 获取类型字典列表

#### 接口信息

- **URL**: `/admin/dictionary/type` 或 `/public/dictionary/type`
- **方法**: `GET`
- **认证**: 管理端需要认证，公共接口无需认证

### 获取类型字典树形结构

#### 接口信息

- **URL**: `/admin/dictionary/type/tree` 或 `/public/dictionary/type/tree`
- **方法**: `GET`
- **认证**: 管理端需要认证，公共接口无需认证

---

## 关系字典管理

### 创建关系字典

#### 接口信息

- **URL**: `/admin/dictionary/relationship`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| relationCode | string | 是 | 关系编码，最大50字符，唯一 |
| relationName | string | 是 | 关系名称，最大255字符 |
| parentId | number | 否 | 父级关系ID |
| status | number | 否 | 状态，0禁用 1启用，默认1 |
| sort | number | 否 | 排序号 |
| relationDesc | string | 否 | 关系描述 |

#### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/dictionary/relationship" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "relationCode": "ADJACENT",
    "relationName": "相邻",
    "status": 1,
    "sort": 1,
    "relationDesc": "地理位置相邻关系"
  }'
```

### 更新关系字典

#### 接口信息

- **URL**: `/admin/dictionary/relationship/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 删除关系字典

#### 接口信息

- **URL**: `/admin/dictionary/relationship/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

### 获取关系字典列表

#### 接口信息

- **URL**: `/admin/dictionary/relationship` 或 `/public/dictionary/relationship`
- **方法**: `GET`
- **认证**: 管理端需要认证，公共接口无需认证

---

## 注意事项

1. **编码唯一性**: 各字典的编码字段必须唯一
2. **层级关系**: 支持父子级关系，可构建树形结构
3. **状态控制**: 通过status字段控制启用/禁用状态
4. **缓存机制**: 字典数据会被缓存，修改后会自动刷新缓存
5. **关联检查**: 删除字典项时会检查是否有关联数据
6. **权限控制**: 管理接口需要管理员权限，查询接口支持公共访问
