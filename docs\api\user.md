# 用户管理接口

## 用户登录

用户登录获取访问令牌。

### 接口信息

- **URL**: `/api/admin/login`
- **方法**: `POST`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名，长度3-50字符 |
| password | string | 是 | 密码，长度6-50字符 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/api/admin/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin",
      "enabled": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  },
  "message": "登录成功"
}
```

---

## 创建用户

创建新用户账户。

### 接口信息

- **URL**: `/api/admin/user`
- **方法**: `POST`
- **认证**: 需要管理员权限

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名，长度3-50字符，唯一 |
| password | string | 是 | 密码，长度6-50字符 |
| role | string | 否 | 角色，可选值：`admin`、`user`，默认`user` |
| enabled | boolean | 否 | 是否启用，默认`true` |

### 请求示例

```bash
curl -X POST "http://localhost:7001/api/admin/user" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "username": "newuser",
    "password": "password123",
    "role": "user",
    "enabled": true
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 2,
    "username": "newuser",
    "role": "user",
    "enabled": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "创建用户成功"
}
```

---

## 获取用户列表

分页获取用户列表。

### 接口信息

- **URL**: `/api/admin/user`
- **方法**: `GET`
- **认证**: 需要管理员权限

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按用户名模糊搜索 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/api/admin/user?page=1&pageSize=10&keyword=admin" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "username": "admin",
        "role": "admin",
        "enabled": true,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "获取用户列表成功"
}
```

---

## 获取用户详情

根据用户ID获取用户详细信息。

### 接口信息

- **URL**: `/api/admin/user/{id}`
- **方法**: `GET`
- **认证**: 需要管理员权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/api/admin/user/1" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "enabled": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "获取用户详情成功"
}
```

---

## 更新用户

更新用户信息。

### 接口信息

- **URL**: `/api/admin/user/{id}`
- **方法**: `PUT`
- **认证**: 需要管理员权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 否 | 用户名，长度3-50字符 |
| password | string | 否 | 密码，长度6-50字符 |
| role | string | 否 | 角色，可选值：`admin`、`user` |
| enabled | boolean | 否 | 是否启用 |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/api/admin/user/2" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "enabled": false
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": 2,
    "username": "newuser",
    "role": "user",
    "enabled": false,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "更新用户成功"
}
```

---

## 删除用户

删除指定用户。

### 接口信息

- **URL**: `/api/admin/user/{id}`
- **方法**: `DELETE`
- **认证**: 需要管理员权限

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求示例

```bash
curl -X DELETE "http://localhost:7001/api/admin/user/2" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "success": true,
  "data": null,
  "message": "删除用户成功"
}
```

---

## 错误响应

### 登录失败

```json
{
  "success": false,
  "message": "用户名或密码错误",
  "code": 401
}
```

### 用户名重复

```json
{
  "success": false,
  "message": "用户名已存在",
  "code": 409
}
```

### 用户不存在

```json
{
  "success": false,
  "message": "用户不存在",
  "code": 400
}
```

## 使用说明

1. **首次登录**: 使用默认管理员账户 `admin/admin123`
2. **Token使用**: 登录成功后将token保存，在后续请求中使用
3. **权限控制**: 只有管理员角色可以管理用户
4. **密码安全**: 密码会自动加密存储，不会明文返回
