import { Provide, Inject } from '@midwayjs/core';
import { ModelCtor } from 'sequelize-typescript';
import { Mountain } from '../entity/mountain.entity';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';
import { CreateMountainDTO, UpdateMountainDTO } from '../dto/entity.dto';
import { CacheService } from './cache.service';
import { BaseService } from '../common/BaseService';

@Provide()
export class MountainService extends BaseService<Mountain> {
  @Inject()
  cacheService: CacheService;

  constructor() {
    super('山塬');
  }

  protected getModel(): ModelCtor<Mountain> {
    return Mountain;
  }

  /**
   * 创建山塬（业务逻辑封装）
   */
  async createMountain(createDto: CreateMountainDTO): Promise<Mountain> {
    await this.validateMountainData(createDto);
    return await this.create(createDto as any);
  }

  /**
   * 更新山塬（业务逻辑封装）
   */
  async updateMountain(
    id: number,
    updateDto: UpdateMountainDTO
  ): Promise<Mountain> {
    await this.validateMountainData(updateDto);
    await this.update({ id }, updateDto as any);
    return (await this.findById(id)) as Mountain;
  }

  /**
   * 删除山塬（业务逻辑封装）
   */
  async deleteMountain(id: number): Promise<void> {
    // TODO: 删除关联的照片和关系数据
    await this.delete({ id });
  }

  /**
   * 分页查询山塬列表
   */
  async findMountainList(
    query: PageQueryDTO
  ): Promise<PageResponseDTO<Mountain>> {
    const { page, pageSize, keyword, regionId } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    if (keyword) {
      whereConditions.name = { [Symbol.for('like')]: `%${keyword}%` };
    }

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({
      query: whereConditions,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });

    return new PageResponseDTO(result.list, result.total || 0, page, pageSize);
  }

  /**
   * 根据区域获取山塬列表
   */
  async findByRegion(regionId: number): Promise<Mountain[]> {
    const result = await this.findAll({
      query: { regionDictId: regionId } as any,
      order: [['name', 'ASC']],
    });
    return result.list;
  }

  /**
   * 获取山塬统计数据
   */
  async getStatistics(regionId?: number): Promise<{
    total: number;
    byRegion: Array<{ regionId: number; regionName: string; count: number }>;
  }> {
    const whereConditions: any = {};
    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({ query: whereConditions });
    const total = result.total || 0;

    // TODO: 实现按区域统计
    const byRegion: Array<{
      regionId: number;
      regionName: string;
      count: number;
    }> = [];

    return {
      total,
      byRegion,
    };
  }

  /**
   * 批量导入山塬数据
   */
  async batchImportMountains(mountains: CreateMountainDTO[]): Promise<void> {
    // 批量验证数据
    for (const mountain of mountains) {
      await this.validateMountainData(mountain);
    }

    await this.batchCreate(mountains as any);
  }

  /**
   * 验证山塬数据
   */
  private async validateMountainData(
    data: CreateMountainDTO | UpdateMountainDTO
  ): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('山塬名称不能为空');
    }

    if (!data.longitude || !data.latitude) {
      throw new Error('经纬度不能为空');
    }

    // 验证经纬度范围
    if (data.longitude < -180 || data.longitude > 180) {
      throw new Error('经度范围应在-180到180之间');
    }

    if (data.latitude < -90 || data.latitude > 90) {
      throw new Error('纬度范围应在-90到90之间');
    }
  }
}
