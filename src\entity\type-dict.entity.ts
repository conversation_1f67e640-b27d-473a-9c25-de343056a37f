import {
  Table,
  Column,
  DataType,
  HasMany,
  BelongsTo,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { HistoricalElement } from './historical-element.entity';

export interface TypeDictAttributes {
  /** ID */
  id: number;
  /** 类型编码 */
  typeCode?: string;
  /** 类型名称 */
  typeName?: string;
  /** 父级类型ID */
  parentId?: number;
  /** 状态 */
  status?: number;
  /** 排序号 */
  sort?: number;
  /** 类型描述 */
  typeDesc?: string;
}

/**
 * 类型字典表模型
 */
@Table({
  tableName: 'type_dict',
  comment: '类型字典表',
})
export class TypeDict
  extends Model<TypeDictAttributes>
  implements TypeDictAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: true,
    comment: '类型编码（唯一）',
    field: 'type_code',
  })
  typeCode: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '类型名称',
    field: 'type_name',
  })
  typeName: string;

  @ForeignKey(() => TypeDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '父级类型ID',
    field: 'parent_id',
  })
  parentId: number;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1启用，0禁用）',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序号',
  })
  sort: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '类型说明',
    field: 'type_desc',
  })
  typeDesc: string;

  // 自关联关系
  @BelongsTo(() => TypeDict, 'parentId')
  parent: TypeDict;

  @HasMany(() => TypeDict, 'parentId')
  children: TypeDict[];

  // 关联关系
  @HasMany(() => HistoricalElement, 'typeDictId')
  historicalElements: HistoricalElement[];
}
