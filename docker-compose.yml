version: '3.8'

services:
  # 智慧营建系统应用
  app:
    build: .
    ports:
      - "7001:7001"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=zhihuiyingjian123
      - DB_DATABASE=zhi_hui_ying_jian
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      - JWT_SECRET=zhi-hui-ying-jian-production-secret-key-2024
      - JWT_EXPIRES_IN=24h
    depends_on:
      - mysql
      - redis
    volumes:
      - ./public/uploads:/app/public/uploads
    restart: unless-stopped
    networks:
      - zhihuiyingjian

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=zhihuiyingjian123
      - MYSQL_DATABASE=zhi_hui_ying_jian
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    networks:
      - zhihuiyingjian

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - zhihuiyingjian

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./public:/usr/share/nginx/html/public
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - zhihuiyingjian

volumes:
  mysql_data:
  redis_data:

networks:
  zhihuiyingjian:
    driver: bridge
