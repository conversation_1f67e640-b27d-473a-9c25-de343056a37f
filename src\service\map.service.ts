import { Provide, Inject } from '@midwayjs/core';
import { MapDataQueryDTO } from '../dto/common.dto';
import { MountainService } from './mountain.service';
import { WaterSystemService } from './water-system.service';
import { HistoricalElementService } from './historical-element.service';

@Provide()
export class MapService {
  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 获取地图数据
   */
  async getMapData(query: MapDataQueryDTO) {
    const { type, regionId, typeId } = query;

    const result: any = {
      mountains: [],
      waterSystems: [],
      historicalElements: [],
    };

    // 根据类型筛选返回对应数据
    if (!type || type === 'mountain') {
      const whereConditions: any = {};
      if (regionId) {
        whereConditions.regionDictId = regionId;
      }

      const mountainResult = await this.mountainService.findAll({
        query: whereConditions,
        attributes: ['id', 'name', 'code', 'longitude', 'latitude', 'height'],
      });
      result.mountains = mountainResult.list;
    }

    if (!type || type === 'water_system') {
      const whereConditions: any = {};
      if (regionId) {
        whereConditions.regionDictId = regionId;
      }

      const waterSystemResult = await this.waterSystemService.findAll({
        query: whereConditions,
        attributes: [
          'id',
          'name',
          'code',
          'longitude',
          'latitude',
          'lengthArea',
        ],
      });
      result.waterSystems = waterSystemResult.list;
    }

    if (!type || type === 'historical_element') {
      const whereConditions: any = {};
      if (regionId) {
        whereConditions.regionDictId = regionId;
      }
      if (typeId) {
        whereConditions.typeDictId = typeId;
      }

      const historicalElementResult =
        await this.historicalElementService.findAll({
          query: whereConditions,
          attributes: [
            'id',
            'name',
            'code',
            'constructionLongitude',
            'constructionLatitude',
            'constructionTime',
          ],
        });
      result.historicalElements = historicalElementResult.list;
    }

    return result;
  }

  /**
   * 获取详情数据
   */
  async getDetailData(type: string, id: number) {
    let entity = null;
    const photos: any[] = [];
    const relationships: any[] = [];

    switch (type) {
      case 'mountain':
        entity = await this.mountainService.findById(id);
        break;
      case 'water_system':
        entity = await this.waterSystemService.findById(id);
        break;
      case 'historical_element':
        entity = await this.historicalElementService.findById(id);
        break;
      default:
        throw new Error('无效的数据类型');
    }

    if (!entity) {
      throw new Error('数据不存在');
    }

    // TODO: 获取关联的照片和关系数据
    // photos = await this.photoService.findByEntity(type, id);
    // relationships = await this.relationshipService.findByEntity(id);

    return {
      entity,
      photos,
      relationships,
    };
  }

  /**
   * 获取地图统计数据
   */
  async getMapStatistics(regionId?: number) {
    const mountainStats = await this.mountainService.getStatistics(regionId);
    const waterSystemStats = await this.waterSystemService.getStatistics(
      regionId
    );
    const historicalElementStats =
      await this.historicalElementService.getStatistics(regionId);

    return {
      mountains: mountainStats,
      waterSystems: waterSystemStats,
      historicalElements: historicalElementStats,
      total: {
        mountain: mountainStats.total,
        waterSystem: waterSystemStats.total,
        historicalElement: historicalElementStats.total,
      },
    };
  }
}
