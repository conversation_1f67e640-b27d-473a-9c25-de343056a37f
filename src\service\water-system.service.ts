import { Provide, Inject } from '@midwayjs/core';
import { ModelCtor } from 'sequelize-typescript';
import { WaterSystem } from '../entity/water-system.entity';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';
import { CreateWaterSystemDTO, UpdateWaterSystemDTO } from '../dto/entity.dto';
import { CacheService } from './cache.service';
import { BaseService } from '../common/BaseService';

@Provide()
export class WaterSystemService extends BaseService<WaterSystem> {
  @Inject()
  cacheService: CacheService;

  constructor() {
    super('水系');
  }

  protected getModel(): ModelCtor<WaterSystem> {
    return WaterSystem;
  }

  /**
   * 创建水系（业务逻辑封装）
   */
  async createWaterSystem(
    createDto: CreateWaterSystemDTO
  ): Promise<WaterSystem> {
    await this.validateWaterSystemData(createDto);
    return await this.create(createDto as any);
  }

  /**
   * 更新水系（业务逻辑封装）
   */
  async updateWaterSystem(
    id: number,
    updateDto: UpdateWaterSystemDTO
  ): Promise<WaterSystem> {
    await this.validateWaterSystemData(updateDto);
    await this.update({ id }, updateDto as any);
    return (await this.findById(id)) as WaterSystem;
  }

  /**
   * 删除水系（业务逻辑封装）
   */
  async deleteWaterSystem(id: number): Promise<void> {
    // TODO: 删除关联的照片和关系数据
    await this.delete({ id });
  }

  /**
   * 分页查询水系列表
   */
  async findWaterSystemList(
    query: PageQueryDTO
  ): Promise<PageResponseDTO<WaterSystem>> {
    const { page, pageSize, keyword, regionId } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    if (keyword) {
      whereConditions.name = { [Symbol.for('like')]: `%${keyword}%` };
    }

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({
      query: whereConditions,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });

    return new PageResponseDTO(result.list, result.total || 0, page, pageSize);
  }

  /**
   * 根据区域获取水系列表
   */
  async findByRegion(regionId: number): Promise<WaterSystem[]> {
    const result = await this.findAll({
      query: { regionDictId: regionId } as any,
      order: [['name', 'ASC']],
    });
    return result.list;
  }

  /**
   * 获取水系统计数据
   */
  async getStatistics(regionId?: number): Promise<{
    total: number;
    totalLength: number;
    byRegion: Array<{ regionId: number; regionName: string; count: number }>;
  }> {
    const whereConditions: any = {};
    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({ query: whereConditions });
    const total = result.total || 0;

    // TODO: 计算总长度和按区域统计
    const totalLength = 0;
    const byRegion: Array<{
      regionId: number;
      regionName: string;
      count: number;
    }> = [];

    return {
      total,
      totalLength,
      byRegion,
    };
  }

  /**
   * 根据长度/面积范围查询
   */
  async findByLengthArea(
    minLength?: number,
    maxLength?: number
  ): Promise<WaterSystem[]> {
    const whereConditions: any = {};

    // TODO: 实现长度/面积范围查询逻辑
    // 需要解析 lengthArea 字段中的数值

    const result = await this.findAll({ query: whereConditions });
    return result.list;
  }

  /**
   * 批量导入水系数据
   */
  async batchImportWaterSystems(
    waterSystems: CreateWaterSystemDTO[]
  ): Promise<void> {
    // 批量验证数据
    for (const waterSystem of waterSystems) {
      await this.validateWaterSystemData(waterSystem);
    }

    await this.batchCreate(waterSystems as any);
  }

  /**
   * 验证水系数据
   */
  private async validateWaterSystemData(
    data: CreateWaterSystemDTO | UpdateWaterSystemDTO
  ): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('水系名称不能为空');
    }

    if (!data.longitude || !data.latitude) {
      throw new Error('经纬度不能为空');
    }

    // 验证经纬度范围
    if (data.longitude < -180 || data.longitude > 180) {
      throw new Error('经度范围应在-180到180之间');
    }

    if (data.latitude < -90 || data.latitude > 90) {
      throw new Error('纬度范围应在-90到90之间');
    }

    // 验证长度/面积格式
    if (data.lengthArea && typeof data.lengthArea === 'string') {
      // TODO: 验证长度/面积字符串格式
    }
  }
}
