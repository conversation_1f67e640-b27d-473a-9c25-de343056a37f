# 智慧营建系统

基于 Midway 框架开发的智慧营建系统后端服务，用于管理和展示关中地区的山塬、水系、历史要素等地理文化数据。

## 系统特性

- 🗺️ **地图数据管理** - 支持山塬、水系、历史要素的地理信息管理
- 📊 **统计分析** - 提供数据统计和时间轴分析功能
- 🔐 **权限控制** - 基于JWT的用户认证和角色权限管理
- 📁 **文件上传** - 支持图片文件上传和管理
- 🔄 **关系管理** - 支持各类地理要素间的关联关系管理
- 📱 **RESTful API** - 提供完整的REST API接口

## 技术栈

- **框架**: Midway.js (Node.js)
- **数据库**: MySQL + TypeORM
- **缓存**: Redis
- **认证**: JWT
- **文件上传**: @midwayjs/busboy
- **数据验证**: @midwayjs/validate

## 快速开始

### 环境要求

- Node.js >= 22.0.0
- MySQL >= 5.7
- Redis >= 5.0

### 安装依赖

```bash
npm install
```

### 配置环境

1. 复制环境变量配置文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的配置：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=zhi_hui_ying_jian

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d
```

### 初始化数据库

1. 创建数据库：
```sql
CREATE DATABASE zhi_hui_ying_jian CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 运行初始化脚本：
```bash
mysql -u root -p zhi_hui_ying_jian < scripts/init-db.sql
```

### 开发环境启动

```bash
npm run dev
```

服务将在 http://localhost:7001 启动

### 生产环境部署

```bash
# 构建项目
npm run build

# 启动服务
npm start
```

## API 文档

### 公共接口

#### 获取地图数据
```
GET /api/map-data?type=mountain&regionId=1
```

#### 获取详情数据
```
GET /api/detail?type=mountain&id=1
```

#### 获取列表数据
```
GET /api/list/mountain?page=1&pageSize=10&regionId=1
```

#### 获取统计数据
```
GET /api/statistic?regionId=1&startTime=2020-01-01&endTime=2024-12-31
```

### 管理端接口

#### 用户登录
```
POST /api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

#### 创建数据
```
POST /api/admin/data/mountain
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "华山",
  "code": "HS001",
  "longitude": 110.0910,
  "latitude": 34.4880,
  "height": 2154,
  "regionDictId": 1
}
```

#### 文件上传
```
POST /api/upload/file
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <image_file>
```

## 数据库结构

### 核心表

- `mountain` - 山塬表
- `water_system` - 水系表
- `historical_element` - 历史要素表
- `dictionary` - 字典表
- `relationship` - 关系表
- `photo` - 照片表
- `user` - 用户表

### 关系说明

- 山塬、水系、历史要素都关联到字典表的区域信息
- 照片表可以关联到山塬、水系或历史要素
- 关系表记录各要素间的关联关系

## 项目结构

```
src/
├── config/           # 配置文件
├── controller/       # 控制器
├── dto/             # 数据传输对象
├── entity/          # 数据库实体
├── filter/          # 异常过滤器
├── middleware/      # 中间件
└── service/         # 服务层
```

## 开发指南

### 添加新的数据类型

1. 在 `src/entity/` 中创建实体类
2. 在 `src/dto/` 中创建对应的DTO
3. 在 `src/service/data.service.ts` 中添加处理逻辑
4. 在控制器中添加相应的接口

### 自定义中间件

在 `src/middleware/` 中创建中间件，并在 `src/configuration.ts` 中注册。

### 数据验证

使用 `@midwayjs/validate` 进行数据验证，在DTO中定义验证规则。

## 部署说明

### Docker 部署

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 7001
CMD ["npm", "start"]
```

### 环境变量

生产环境需要设置以下环境变量：
- `NODE_ENV=production`
- `DB_HOST`, `DB_PORT`, `DB_USERNAME`, `DB_PASSWORD`, `DB_DATABASE`
- `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD`
- `JWT_SECRET`

## 许可证

MIT License
