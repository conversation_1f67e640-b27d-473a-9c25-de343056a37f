import {
  Table,
  Column,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { RegionDict } from './region-dict.entity';
import { TypeDict } from './type-dict.entity';
import { Photo } from './photo.entity';

export interface HistoricalElementAttributes {
  /** ID */
  id: number;
}

/**
 * 历史要素表模型
 */
@Table({
  tableName: 'historical_element',
  comment: '历史要素表',
})
export class HistoricalElement
  extends Model<HistoricalElementAttributes>
  implements HistoricalElementAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '编号',
  })
  code: string;

  @ForeignKey(() => TypeDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '所属类型ID',
    field: 'type_dict_id',
  })
  typeDictId: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: false,
    comment: '建筑经度',
    field: 'construction_longitude',
  })
  constructionLongitude: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: false,
    comment: '建筑纬度',
    field: 'construction_latitude',
  })
  constructionLatitude: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '位置描述',
    field: 'location_description',
  })
  locationDescription: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '建筑时间',
    field: 'construction_time',
  })
  constructionTime: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '历史记载',
    field: 'historical_records',
  })
  historicalRecords: string;

  @ForeignKey(() => RegionDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属区域ID',
    field: 'region_dict_id',
  })
  regionDictId: number;

  // 关联关系
  @BelongsTo(() => TypeDict, 'typeDictId')
  typeDict: TypeDict;

  @BelongsTo(() => RegionDict, 'regionDictId')
  regionDict: RegionDict;

  @HasMany(() => Photo, 'historicalElementId')
  photos: Photo[];
}
