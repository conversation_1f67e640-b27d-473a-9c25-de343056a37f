import { Provide, Inject } from '@midwayjs/core';
import { StatisticQueryDTO } from '../dto/common.dto';
import { MountainService } from './mountain.service';
import { WaterSystemService } from './water-system.service';
import { HistoricalElementService } from './historical-element.service';
import { DictionaryService } from './dictionary.service';

@Provide()
export class StatisticService {
  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  @Inject()
  dictionaryService: DictionaryService;

  /**
   * 获取统计数据
   */
  async getStatisticData(query: StatisticQueryDTO) {
    const { regionId, startTime, endTime } = query;

    // 数量统计
    const mountainCount = await this.getMountainCount(regionId);
    const waterSystemCount = await this.getWaterSystemCount(regionId);
    const historicalElementCount = await this.getHistoricalElementCount(
      regionId,
      startTime,
      endTime
    );

    // 区域分布统计
    const regionStats = await this.getRegionStats();

    // 时间轴数据（历史要素按时间分布）
    const timelineData = await this.getTimelineData(
      regionId,
      startTime,
      endTime
    );

    return {
      counts: {
        mountain: mountainCount,
        waterSystem: waterSystemCount,
        historicalElement: historicalElementCount,
      },
      regionStats,
      timelineData,
    };
  }

  /**
   * 获取山塬数量统计
   */
  private async getMountainCount(regionId?: number): Promise<number> {
    const whereConditions: any = {};
    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.mountainService.findAll({
      query: whereConditions,
    });

    return result.total || 0;
  }

  /**
   * 获取水系数量统计
   */
  private async getWaterSystemCount(regionId?: number): Promise<number> {
    const whereConditions: any = {};
    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.waterSystemService.findAll({
      query: whereConditions,
    });

    return result.total || 0;
  }

  /**
   * 获取历史要素数量统计
   */
  private async getHistoricalElementCount(
    regionId?: number,
    startTime?: Date,
    endTime?: Date
  ): Promise<number> {
    const whereConditions: any = {};

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    if (startTime) {
      whereConditions.constructionTime = { [Symbol.for('gte')]: startTime };
    }

    if (endTime) {
      if (whereConditions.constructionTime) {
        whereConditions.constructionTime[Symbol.for('lte')] = endTime;
      } else {
        whereConditions.constructionTime = { [Symbol.for('lte')]: endTime };
      }
    }

    const result = await this.historicalElementService.findAll({
      query: whereConditions,
    });

    return result.total || 0;
  }

  /**
   * 获取区域分布统计
   */
  private async getRegionStats() {
    const regions = await this.dictionaryService.getRegionDictList();
    const stats = [];

    for (const region of regions) {
      const mountainCount = await this.getMountainCount(region.id);
      const waterSystemCount = await this.getWaterSystemCount(region.id);
      const historicalElementCount = await this.getHistoricalElementCount(
        region.id
      );

      stats.push({
        region: region.regionName,
        regionId: region.id,
        mountainCount,
        waterSystemCount,
        historicalElementCount,
        total: mountainCount + waterSystemCount + historicalElementCount,
      });
    }

    return stats;
  }

  /**
   * 获取时间轴数据
   */
  private async getTimelineData(
    regionId?: number,
    startTime?: Date,
    endTime?: Date
  ) {
    return await this.historicalElementService.getTimelineData(regionId);
  }

  /**
   * 获取综合统计报告
   */
  async getComprehensiveReport(regionId?: number) {
    const basicStats = await this.getStatisticData({ regionId });

    // 获取各类型详细统计
    const mountainStats = await this.mountainService.getStatistics(regionId);
    const waterSystemStats = await this.waterSystemService.getStatistics(
      regionId
    );
    const historicalElementStats =
      await this.historicalElementService.getStatistics(regionId);

    return {
      basic: basicStats,
      detailed: {
        mountains: mountainStats,
        waterSystems: waterSystemStats,
        historicalElements: historicalElementStats,
      },
      summary: {
        totalEntities:
          basicStats.counts.mountain +
          basicStats.counts.waterSystem +
          basicStats.counts.historicalElement,
        regionCoverage: basicStats.regionStats.length,
        timeSpan: this.calculateTimeSpan(basicStats.timelineData),
      },
    };
  }

  /**
   * 计算时间跨度
   */
  private calculateTimeSpan(timelineData: any[]): {
    earliest: number | null;
    latest: number | null;
    span: number;
  } {
    if (!timelineData || timelineData.length === 0) {
      return { earliest: null, latest: null, span: 0 };
    }

    const years = timelineData.map(item => item.year).sort((a, b) => a - b);
    const earliest = years[0];
    const latest = years[years.length - 1];

    return {
      earliest,
      latest,
      span: latest - earliest,
    };
  }
}
