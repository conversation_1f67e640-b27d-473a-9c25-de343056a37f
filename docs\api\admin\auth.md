# 认证管理接口

## 概述

管理后台的用户认证相关接口，包括登录、获取用户信息、退出登录等功能。

---

## 管理员登录

### 接口信息

- **URL**: `/admin/auth/login`
- **方法**: `POST`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名，长度3-50字符 |
| password | string | 是 | 密码，长度6-50字符 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 响应示例

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "enabled": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

---

## 获取当前用户信息

### 接口信息

- **URL**: `/admin/auth/profile`
- **方法**: `GET`
- **认证**: 需要JWT Token

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/auth/profile" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "id": 1,
  "username": "admin",
  "role": "admin",
  "permissions": ["*"]
}
```

---

## 退出登录

### 接口信息

- **URL**: `/admin/auth/logout`
- **方法**: `POST`
- **认证**: 需要JWT Token

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/auth/logout" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "message": "退出成功"
}
```

---

## 刷新Token

### 接口信息

- **URL**: `/admin/auth/refresh`
- **方法**: `POST`
- **认证**: 需要JWT Token

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/auth/refresh" \
  -H "Authorization: Bearer <token>"
```

### 响应示例

```json
{
  "message": "Token刷新成功"
}
```

---

## 错误响应

### 登录失败

```json
{
  "message": "用户名或密码错误",
  "statusCode": 401,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/admin/auth/login"
}
```

### Token无效

```json
{
  "message": "认证令牌无效或已过期",
  "statusCode": 401,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/admin/auth/profile"
}
```

### 权限不足

```json
{
  "message": "权限不足",
  "statusCode": 403,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/admin/mountain"
}
```

---

## 使用说明

### 1. 认证流程

1. 使用用户名密码调用登录接口
2. 获取返回的JWT Token
3. 在后续请求的Header中携带Token：`Authorization: Bearer <token>`
4. Token过期后需要重新登录

### 2. 默认账户

- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: `admin`

### 3. Token使用

```javascript
// 登录获取Token
const loginResponse = await fetch('/admin/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'admin', password: 'admin123' })
});
const { token } = await loginResponse.json();

// 使用Token访问受保护的接口
const dataResponse = await fetch('/admin/mountain', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### 4. 权限说明

- **admin角色**: 拥有所有权限，可以访问所有管理接口
- **user角色**: 普通用户权限（如果系统支持）
- 所有 `/admin/*` 路径的接口都需要认证
- 所有 `/public/*` 路径的接口无需认证

### 5. 安全建议

1. **Token存储**: 建议存储在内存中，避免存储在localStorage
2. **HTTPS**: 生产环境必须使用HTTPS
3. **Token过期**: 合理设置Token过期时间
4. **密码安全**: 定期更换默认密码
5. **权限控制**: 根据实际需求配置用户权限
