import { MidwayConfig } from '@midwayjs/core';

export default {
  // Sequelize 数据库配置
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '***********',
        port: 3306,
        username: 'remote_user',
        password: 'Aa@123456',
        database: 'zhi_hui_ying_jian',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          // max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        // 数据库连接选项
        dialectOptions: {
          connectTimeout: 60000,
          supportBigNumbers: true,
          bigNumberStrings: true,
          debug: false,
        },
      },
    },
  },
  // Redis 配置
  redis: {
    clients: {
      default: {
        port: 6379,
        host: '***********',
        password: 'Aa@123456',
        db: 0,
      },
      dictionary: {
        port: 6379,
        host: '***********',
        password: 'Aa@123456',
        db: 1,
      },
    },
  },
  // 上传文件存储路径配置
  upload: {
    uploadPath: '/projects/html/uploads',
    // 文件访问基础URL
    baseUrl: 'http://localhost:8080/uploads',
  },
  // 生产环境日志配置
  midwayLogger: {
    default: {
      level: 'info',
      consoleLevel: 'warn',
    },
  },
} as MidwayConfig;
