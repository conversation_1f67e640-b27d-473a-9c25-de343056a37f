import { Controller, Post, Get, Body, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { UserService } from '../../service/user.service';
import { LoginDTO } from '../../dto/user.dto';
import { JwtService } from '@midwayjs/jwt';

/**
 * 管理后台认证控制器
 */
@Controller('/admin/auth')
export class AdminAuthController {
  @Inject()
  ctx: Context;

  @Inject()
  userService: UserService;

  @Inject()
  jwtService: JwtService;

  /**
   * 管理员登录
   */
  @Post('/login')
  @Validate()
  async login(@Body() loginDto: LoginDTO) {
    const result = await this.userService.login(loginDto);
    return result;
  }

  /**
   * 获取当前用户信息
   */
  @Get('/profile', { middleware: [JwtMiddleware] })
  async getProfile() {
    // 从JWT中间件设置的用户信息获取当前用户
    const currentUser = this.ctx.state.user;

    if (!currentUser) {
      throw new Error('用户信息获取失败');
    }

    // 获取完整的用户信息
    const user = await this.userService.getUserById(currentUser.id);

    // 根据角色设置权限
    const permissions = this.getPermissionsByRole(user.role);

    return {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      email: user.email,
      avatar: user.avatar,
      role: user.role,
      isActive: user.isActive,
      permissions,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  /**
   * 根据角色获取权限列表
   */
  private getPermissionsByRole(role: string): string[] {
    switch (role) {
      case 'admin':
        return ['*']; // 管理员拥有所有权限
      case 'user':
        return ['read']; // 普通用户只有读权限
      default:
        return [];
    }
  }

  /**
   * 退出登录
   */
  @Post('/logout', { middleware: [JwtMiddleware] })
  async logout() {
    // 获取当前用户信息
    const currentUser = this.ctx.state.user;

    if (!currentUser) {
      throw new Error('用户未登录');
    }

    // 在实际应用中，可以在这里实现以下逻辑：
    // 1. 将token加入黑名单（需要Redis支持）
    // 2. 记录登出日志
    // 3. 清理用户相关的缓存数据

    // 目前JWT是无状态的，客户端删除token即可实现登出
    // 这里可以记录登出日志
    console.log(`用户 ${currentUser.username} (ID: ${currentUser.id}) 已登出`);

    return {
      message: '退出成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 刷新Token
   */
  @Post('/refresh', { middleware: [JwtMiddleware] })
  async refreshToken() {
    // 获取当前用户信息
    const currentUser = this.ctx.state.user;

    if (!currentUser) {
      throw new Error('用户未登录');
    }

    // 验证用户是否仍然有效
    const user = await this.userService.validateUser(currentUser.id);

    // 生成新的JWT Token
    const newToken = await this.jwtService.sign({
      userId: user.id,
      username: user.username,
      role: user.role,
    });

    // 记录token刷新日志
    console.log(`用户 ${user.username} (ID: ${user.id}) 刷新了Token`);

    return {
      message: 'Token刷新成功',
      token: newToken,
      expiresIn: '7d', // 根据配置返回过期时间
      timestamp: new Date().toISOString(),
    };
  }
}
