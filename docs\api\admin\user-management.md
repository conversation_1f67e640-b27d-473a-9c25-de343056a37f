# 用户管理 API 文档

## 概述

用户管理模块提供系统用户的完整管理功能，包括用户的创建、更新、删除、查询、密码管理等操作。

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

### 常见错误码
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 业务错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数校验错误 |
| 500 | 服务器内部错误 |

---

## 创建用户

### 接口信息

- **URL**: `/admin/user`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名，3-50字符，唯一 |
| password | string | 是 | 密码，6-50字符 |
| nickname | string | 否 | 昵称，最大100字符 |
| email | string | 否 | 邮箱，需符合邮箱格式 |
| avatar | string | 否 | 头像URL |
| role | string | 否 | 角色，可选值：admin、user，默认user |
| isActive | boolean | 否 | 是否启用，默认true |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/user" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "role": "user",
    "isActive": true
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 2,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "avatar": null,
    "role": "user",
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

---

## 更新用户

### 接口信息

- **URL**: `/admin/user/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 否 | 用户名，3-50字符 |
| password | string | 否 | 密码，6-50字符 |
| nickname | string | 否 | 昵称，最大100字符 |
| email | string | 否 | 邮箱 |
| avatar | string | 否 | 头像URL |
| role | string | 否 | 角色，可选值：admin、user |
| isActive | boolean | 否 | 是否启用 |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/admin/user/2" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "新昵称",
    "email": "<EMAIL>",
    "isActive": false
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 2,
    "username": "testuser",
    "nickname": "新昵称",
    "email": "<EMAIL>",
    "avatar": null,
    "role": "user",
    "isActive": false,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

---

## 删除用户

### 接口信息

- **URL**: `/admin/user/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求示例

```bash
curl -X DELETE "http://localhost:7001/admin/user/2" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "删除成功"
  }
}
```

---

## 获取用户详情

### 接口信息

- **URL**: `/admin/user/{id}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/user/2" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 2,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "avatar": null,
    "role": "user",
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

---

## 获取用户列表

### 接口信息

- **URL**: `/admin/user`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按用户名、昵称、邮箱模糊搜索 |
| role | string | 否 | 角色筛选，可选值：admin、user |
| isActive | boolean | 否 | 状态筛选，true启用、false禁用 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/user?page=1&pageSize=10&keyword=test&role=user" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "data": [
      {
        "id": 2,
        "username": "testuser",
        "nickname": "测试用户",
        "email": "<EMAIL>",
        "avatar": null,
        "role": "user",
        "isActive": true,
        "createdAt": "2024-01-15T10:00:00.000Z",
        "updatedAt": "2024-01-15T10:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

## 重置用户密码

### 接口信息

- **URL**: `/admin/user/{id}/reset-password`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| newPassword | string | 是 | 新密码，6-50字符 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/user/2/reset-password" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "newPassword": "newpassword123"
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "密码重置成功"
  }
}
```

---

## 启用/禁用用户

### 接口信息

- **URL**: `/admin/user/{id}/toggle-status`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 用户ID |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/user/2/toggle-status" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "用户已禁用",
    "isActive": false
  }
}
```

---

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 用户唯一标识 |
| username | string | 用户名，唯一 |
| nickname | string | 昵称 |
| email | string | 邮箱，唯一 |
| avatar | string | 头像URL |
| role | string | 角色，admin管理员、user普通用户 |
| isActive | boolean | 是否启用，true启用、false禁用 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

---

## 错误处理

### 常见错误

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 用户名已存在 | 创建或更新用户时用户名重复 |
| 400 | 邮箱已存在 | 创建或更新用户时邮箱重复 |
| 400 | 用户不存在 | 操作的用户ID不存在 |
| 400 | 不能删除管理员用户 | 尝试删除admin角色用户 |
| 401 | 未提供认证令牌 | 缺少Authorization头 |
| 401 | 认证令牌无效或已过期 | Token无效 |
| 403 | 权限不足，需要管理员权限 | 非admin角色访问管理接口 |
| 422 | 参数校验错误 | 请求参数不符合验证规则 |

---

## 注意事项

1. **权限控制**:
   - 所有用户管理接口都需要管理员权限
   - 只有admin角色可以访问这些接口
   - 不能删除admin角色的用户

2. **数据验证**:
   - 用户名必须唯一，3-50字符
   - 密码最少6字符，最多50字符
   - 邮箱必须符合邮箱格式且唯一
   - 昵称最大100字符

3. **密码安全**:
   - 密码在存储时会自动加密
   - 重置密码不需要原密码验证
   - 建议定期提醒用户更换密码

4. **用户状态**:
   - 禁用的用户无法登录系统
   - 可以通过toggle-status接口快速切换用户状态
   - 删除用户是物理删除，无法恢复

5. **搜索功能**:
   - 支持按用户名、昵称、邮箱模糊搜索
   - 支持按角色和状态筛选
   - 结果按创建时间倒序排列
