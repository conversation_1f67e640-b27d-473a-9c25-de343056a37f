# 地图数据接口

## 获取地图数据

获取用于地图展示的山塬、水系、历史要素数据。

### 接口信息

- **URL**: `/public/map/data`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 数据类型，可选值：`mountain`、`water_system`、`historical_element`。不传则返回所有类型 |
| regionId | number | 否 | 区域ID，筛选指定区域的数据 |
| typeId | number | 否 | 类型ID，筛选指定类型的历史要素（仅当type=historical_element时有效） |

### 请求示例

```bash
# 获取所有地图数据
curl -X GET "http://localhost:7001/public/map/data"

# 获取指定类型的数据
curl -X GET "http://localhost:7001/public/map/data?type=mountain"

# 获取指定区域的数据
curl -X GET "http://localhost:7001/public/map/data?regionId=1"

# 获取指定区域的山塬数据
curl -X GET "http://localhost:7001/public/map/data?type=mountain&regionId=1"

# 获取指定类型的历史要素
curl -X GET "http://localhost:7001/public/map/data?type=historical_element&typeId=1"
```

### 响应示例

```json
{
  "mountains": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2154,
        "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
        "regionDictId": 1,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "regionDict": {
          "id": 1,
          "type": "region",
          "code": "guanzhong",
          "name": "关中地区"
        }
      }
    ],
    "waterSystems": [
      {
        "id": 1,
        "name": "渭河",
        "code": "WH001",
        "longitude": 108.9400,
        "latitude": 34.2600,
        "lengthArea": "818公里",
        "historicalRecords": "渭河是黄河的最大支流，发源于甘肃省渭源县鸟鼠山。",
        "regionDictId": 1,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "regionDict": {
          "id": 1,
          "type": "region",
          "code": "guanzhong",
          "name": "关中地区"
        }
      }
    ],
    "historicalElements": [
      {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001",
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "locationDescription": "位于西安市雁塔区大慈恩寺内",
        "constructionTime": "652-01-01T00:00:00.000Z",
        "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
        "regionDictId": 2,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "regionDict": {
          "id": 2,
          "type": "region",
          "code": "changan",
          "name": "长安区域"
        }
      }
    ]
}
```

### 数据字段说明

#### 山塬数据 (mountains)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 山塬ID |
| name | string | 山塬名称 |
| code | string | 山塬编号 |
| longitude | number | 经度 |
| latitude | number | 纬度 |
| height | number | 高度（米） |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域信息 |

#### 水系数据 (waterSystems)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 水系ID |
| name | string | 水系名称 |
| code | string | 水系编号 |
| longitude | number | 经度 |
| latitude | number | 纬度 |
| lengthArea | string | 长度/面积 |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域信息 |

#### 历史要素数据 (historicalElements)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 历史要素ID |
| name | string | 名称 |
| code | string | 编号 |
| constructionLongitude | number | 建筑经度 |
| constructionLatitude | number | 建筑纬度 |
| locationDescription | string | 位置描述 |
| constructionTime | string | 建筑时间 |
| historicalRecords | string | 历史记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域信息 |

### 错误响应

```json
{
  "success": false,
  "message": "无效的数据类型",
  "code": 400,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/map-data"
}
```

### 使用场景

1. **地图初始化**: 前端地图组件加载时获取所有数据
2. **类型筛选**: 用户选择只显示特定类型的数据
3. **区域筛选**: 用户选择查看特定区域的数据
4. **组合筛选**: 同时按类型和区域筛选数据

### 前端集成示例

```javascript
// 获取所有地图数据
async function getMapData(type = '', regionId = '') {
  const params = new URLSearchParams();
  if (type) params.append('type', type);
  if (regionId) params.append('regionId', regionId);
  
  const response = await fetch(`/api/map-data?${params}`);
  const result = await response.json();
  
  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 使用示例
getMapData('mountain', 1).then(data => {
  // 在地图上显示山塬数据
  data.mountains.forEach(mountain => {
    addMarkerToMap(mountain.longitude, mountain.latitude, mountain.name);
  });
});
```
